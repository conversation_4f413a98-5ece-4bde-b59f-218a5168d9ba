'use strict';

const path = require('path');
const autoprefixer = require('autoprefixer');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ManifestPlugin = require('webpack-manifest-plugin');
const webpack = require('webpack');
const constants = require('./constants');

// Preparation
const resolve = {
  extensions: ['.tsx', '.ts', '.js', '.css', '.scss'],
  alias: {
    'data-api-alias': path.resolve(process.cwd(), constants.jssDataApiImplementationProdPath),
    Foundation: path.resolve(process.cwd(), './Foundation/'),
    Project: path.resolve(process.cwd(), './Project/'),
    Feature: path.resolve(process.cwd(), './Feature/'),
  },
};

const extractSass = new MiniCssExtractPlugin({
  filename: '[name].[Contenthash].css',
  chunkFilename: '[name].[Contenthash].css',
});

const isDevMode = (mode) => mode === 'development';

const clientWebpackConfigFactory = (projectManifest) => {
  const {
    publicPath,
    entryPath,
    outputPath,
    linter,
    minimize,
    mode,
    dirname = __dirname,
    cleanDir = true,
  } = projectManifest;
  const { apiKey } = projectManifest.env[0];
  const config = {
    mode,
    target: 'web',
    devtool: isDevMode(mode) ? 'inline-source-map' : false,
    entry: {
      'spa-client': entryPath + '/spa-client.index.tsx',
      'mvc-client': entryPath + '/mvc-client.index.tsx',
      'spx-modals': './Project/spx-modals.scss',
      'tailwind-styles': './src/styles/tailwind.css',
    },
    output: {
      path: path.resolve(dirname, outputPath),
      // SPX is hadcoded here, because we have to specify the solution name
      // TODO: remove hardcoded solution name from the publicPath
      publicPath,
      filename: '[name].bundle.[Contenthash].js',
    },
    optimization: {
      minimize,
      usedExports: true,
      sideEffects: true,
      concatenateModules: true,
      runtimeChunk: 'single',
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          'dsm-react': {
            chunks: 'initial',
            test: /[\\/]node_modules[\\/](@pmi[\\/]dsm-react|@mui)/,
            filename: '[name].bundle.[Contenthash].js',
            priority: -10,
            name: 'dsm-react',
            enforce: true,
          },
          vendors: {
            chunks: 'initial',
            name: 'vendors',
            filename: '[name].bundle.[Contenthash].js',
            test: /[\\/]node_modules[\\/]((?!(@pmi[\\/]dsm-react|@mui)).*)$/,
            priority: 10,
            enforce: true,
            minChunks: 1,
          },
          'base-client': {
            name: 'base-client',
            filename: '[name].bundle.[Contenthash].js',
            test: /[\\/](Foundation|Feature)[\\/]/,
            enforce: true,
            chunks: 'initial',
            minChunks: 1,
          },
          default: false,
        },
      },
    },
    resolve,
    module: {
      rules: [
        {
          test: [/\.jpe?g$/, /\.png$/],
          loader: require.resolve('file-loader'),
          options: {
            name: '[name].[hash].[ext]',
          },
        },
        {
          test: /\.gif$/,
          loader: require.resolve('file-loader'),
          options: {
            name: '[name].[hash].[ext]',
            esModule: false,
          },
        },
        {
          test: /\.svg$/,
          loader: require.resolve('file-loader'),
          options: {
            name: '[name].[hash].[ext]',
          },
        },
        {
          test: /\.ico$/,
          loader: require.resolve('file-loader'),
          options: {
            name: '[name].[hash].[ext]',
          },
        },
        {
          test: [/\.ttf$/, /\.otf$/, /\.eot/, /\.woff/],
          loader: require.resolve('url-loader'),
          options: {
            limit: 8192,
            name: '[name].[ext]',
          },
        },
        {
          test: /\.(ts|tsx)$/,
          exclude: /node_modules/,
          use: [
            {
              loader: require.resolve('ts-loader'),
              options: {
                // disable type checker - we will use it in fork plugin
                transpileOnly: true,
              },
            },
          ],
        },
        {
          test: /\.css$/,
          exclude: /\.scss$/,
          use: [
            {
              loader: MiniCssExtractPlugin.loader,
            },
            {
              loader: 'css-loader',
              options: {
                importLoaders: 1,
                sourceMap: mode === 'development',
              },
            },
            {
              loader: require.resolve('postcss-loader'),
              options: {
                ident: 'postcss',
                plugins: () => [
                  require('tailwindcss'),
                  require('postcss-flexbugs-fixes'),
                  require('postcss-object-fit-images'),
                  autoprefixer({ flexbox: 'no-2009' }),
                ],
              },
            },
          ],
        },
        {
          test: /\.scss$/,
          use: [
            {
              loader: MiniCssExtractPlugin.loader,
            },
            {
              loader: 'css-loader',
              options: {
                importLoaders: 2,
                sourceMap: mode === 'development',
              },
            },
            {
              loader: require.resolve('postcss-loader'),
              options: {
                // Necessary for external CSS imports to work
                // https://github.com/facebookincubator/create-react-app/issues/2677
                ident: 'postcss',
                plugins: () => [
                  require('postcss-flexbugs-fixes'),
                  require('postcss-object-fit-images'),
                  autoprefixer({ flexbox: 'no-2009' }),
                ],
              },
            },
            {
              loader: 'resolve-url-loader',
            },
            {
              loader: require.resolve('sass-loader'),
              options: {
                sourceMap: true,
              },
            },
          ],
        },
      ],
    },
    plugins: [
      cleanDir && new CleanWebpackPlugin({ cleanOnceBeforeBuildPatterns: ['!server.bundle.js'] }),
      new webpack.DefinePlugin({
        'process.env.API_KEY': JSON.stringify(apiKey),
      }),
      extractSass,
      new ManifestPlugin({
        publicPath: '',
        fileName: 'assetMap.json',
        serialize: (manifest) => JSON.stringify(manifest),
      }),
    ].filter(Boolean),
  };
  if (linter === 'enabled') {
    config.plugins.splice(
      1,
      0,
      new ForkTsCheckerWebpackPlugin({
        async: false,
        include: ['/Project/', '/Feature/', '/Foundation/'],
        tsconfig: './tsconfig.json',
        tslint: './tslint.json',
      }),
    );
  }
  if (isDevMode(mode)) {
    config.devtool = 'source-map';
    config.watchOptions = {
      ignored: /node_modules/,
    };
  }
  return config;
};

const serverWebpackConfigFactory = (projectManifest) => {
  const { entryPath, outputPath, publicPath, linter, minimize, dirname = __dirname, cleanDir = true } = projectManifest;
  const config = {
    mode: projectManifest.mode,
    target: 'node',
    entry: {
      server: entryPath + '/server.index.tsx',
    },
    output: {
      path: path.resolve(dirname, outputPath),
      publicPath,
      filename: '[name].bundle.js',
      libraryTarget: 'this', // this option is required for use with JavaScriptViewEngine
    },
    optimization: {
      minimize,
      usedExports: true,
      sideEffects: true,
      concatenateModules: true,
    },
    resolve,
    module: {
      rules: [
        {
          test: [/\.jpe?g$/, /\.png$/, /\.ttf$/, /\.otf$/, /\.eot/, /\.woff/, /\.ico/],
          loader: 'url-loader',
          options: {
            limit: 8192,
            name: '[name].[ext]',
            useRelativePath: false,
          },
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: 'svg-url-loader',
              options: {
                limit: 8192,
                name: '[name].[ext]',
              },
            },
          ],
        },
        {
          test: /\.ico/,
          use: 'file-loader?name=[name].[ext]',
        },
        {
          test: /\.(ts|tsx)$/,
          exclude: /node_modules/,
          use: [
            {
              loader: require.resolve('ts-loader'),
              options: {
                // disable type checker - we will use it in fork plugin
                transpileOnly: true,
              },
            },
          ],
        },
        { test: /\.scss$/, loader: 'ignore-loader' },
      ],
    },
    plugins: [
      cleanDir && new CleanWebpackPlugin(),
      new webpack.DefinePlugin({
        'process.env.BUNDLE_PUBLIC_PATH': JSON.stringify(publicPath),
      }),
    ].filter(Boolean),
  };

  if (linter === 'enabled') {
    config.plugins.splice(
      1,
      0,
      new ForkTsCheckerWebpackPlugin({
        async: false,
        include: ['/Project/', '/Feature/', '/Foundation/'],
        memoryLimit: 2048,
        tsconfig: './tsconfig.json',
        tslint: './tslint.json',
        tslintAutoFix: true,
      }),
    );
  }
  return config;
};

module.exports = (manifest) => [clientWebpackConfigFactory(manifest), serverWebpackConfigFactory(manifest)];
