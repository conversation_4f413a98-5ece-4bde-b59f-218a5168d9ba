{"name": "SPX", "version": "1.0.0", "description": "SPX", "homepage": "", "engines": {"node": "^22.5.1", "npm": "^10.8.2", "yarn": "yarn is not supported, please use npm"}, "sideEffects": ["*.scss", "*.css"], "scripts": {"start": "node ./scripts/webpack/environments/development/server.js --project=SPX --env=local", "build": "cross-env NODE_ENV=production webpack", "debug": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider webpack --watch --progress", "test": "jest --no-cache --config ./scripts/jest/jest.config.js || exit 0", "test-watch": "jest --watch --notify --config ./scripts/jest/jest.config.js", "test-cover": "jest --ci --coverage --config ./scripts/jest/jest.config.js", "tslint": "tslint --fix --project tsconfig.json", "refreshVSToken": "vsts-npm-auth -config .npmrc"}, "devDependencies": {"@cfaester/enzyme-adapter-react-18": "^0.7.0", "@redux-saga/testing-utils": "~1.0.2", "@testing-library/react": "^14.0.0", "@testing-library/react-hooks": "^7.0.2", "@types/cheerio": "^0.22.22", "@types/classnames": "^2.2.7", "@types/credit-card-type": "^7.0.0", "@types/enzyme": "^3.10.5", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/history": "^4.7.2", "@types/jest": "^29.4.4", "@types/node": "^22.5.1", "@types/prop-types": "^15.7.0", "@types/query-string": "^5.1.0", "@types/react": "^18.3.16", "@types/react-bootstrap": "^0.32.17", "@types/react-dom": "^18.0.11", "@types/react-helmet": "^5.0.15", "@types/react-input-mask": "^2.0.3", "@types/react-redux": "^7.1.0", "@types/react-router": "^4.4.5", "@types/react-router-dom": "^4.3.1", "@types/react-slick": "^0.23.4", "@types/react-test-renderer": "^18.0.0", "@types/react-text-mask": "^5.4.6", "@types/react-truncate": "^2.3.3", "@types/redux": "^3.6.0", "@types/redux-actions": "^2.6.1", "@types/redux-mock-store": "^1.0.2", "@types/url-parse": "^1.4.3", "autoprefixer": "^9.7.4", "babel-cli": "^6.26.0", "babel-loader": "^8.0.6", "babel-preset-env": "^1.7.0", "clean-webpack-plugin": "^3.0.0", "colors": "^1.4.0", "cross-env": "^7.0.0", "css-loader": "^3.4.2", "cssnano": "^4.1.10", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "enzyme-to-json": "^3.5.0", "file-loader": "^5.1.0", "fork-ts-checker-webpack-plugin": "^4.1.6", "glob": "^7.1.6", "handlebars": "4.5.0", "identity-obj-proxy": "^3.0.0", "ignore-loader": "^0.1.2", "inquirer": "~6.2.2", "jest": "^22.4.4", "jest-canvas-mock": "^2.4.0", "jest-junit": "^10.0.0", "jest-trx-results-processor": "0.0.7", "merge-stream": "^2.0.0", "mini-css-extract-plugin": "^0.9.0", "postcss": "^8.5.5", "postcss-flexbugs-fixes": "^4.2.0", "postcss-loader": "^3.0.0", "postcss-object-fit-images": "^1.1.2", "prettier": "^3.5.1", "react-test-renderer": "^18.2.0", "redux-mock-store": "^1.5.4", "resolve-url-loader": "^5.0.0", "sass": "^1.54.0", "sass-loader": "^10.1.1", "style-loader": "^1.1.3", "stylelint": "^16.14.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-prettier": "^5.0.3", "svg-url-loader": "^4.0.0", "tailwindcss": "^3.4.17", "through2": "^3.0.1", "ts-jest": "^22.4.6", "ts-loader": "^8.4.0", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-react": "^4.0.0", "tslint-react-hooks": "^2.2.1", "tslint-sonarts": "^1.9.0", "typescript": "^4.9.5", "url-loader": "^3.0.0", "webpack": "^4.47.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.2", "webpack-manifest-plugin": "^2.2.0", "yargs": "^11.1.0"}, "dependencies": {"@adyen/adyen-web": "^5.60.0", "@babel/polyfill": "^7.8.7", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.1", "@pmi/analytics-layer": "^1.0.52", "@pmi/dsm-react": "^15.0.1", "@sitecore-jss/sitecore-jss-react": "^20.0.0", "@tanstack/react-query": "^4.32.6", "@tanstack/react-query-devtools": "^4.32.6", "axios": "^0.18.0", "bootstrap": "^5.2.3", "card-validator": "^9.0.0", "classnames": "^2.2.6", "connected-react-router": "~6.5.0", "cpf-cnpj-validator": "1.0.2", "credit-card-type": "^8.2.0", "crypto-hash": "^1.3.0", "custom-event-polyfill": "^1.0.7", "graphql": "^15.5.0", "graphql-request": "^4.3.0", "history": "^4.9.0", "isomorphic-fetch": "^3.0.0", "jotai": "^2.4.3", "jotai-location": "^0.5.2", "logrocket": "^9.0.0", "re-reselect": "^3.4.0", "react": "^18.2.0", "react-bootstrap": "^0.33.1", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-helmet": "^5.2.1", "react-hook-form": "^7.45.4", "react-input-mask": "^2.0.4", "react-load-script": "0.0.6", "react-number-format": "^4.3.1", "react-redux": "^7.1.0", "react-router": "^5.0.0", "react-router-dom": "^5.0.0", "react-slick": "^0.25.2", "react-text-mask": "^5.4.3", "react-truncate": "^2.4.0", "redux": "^4.0.1", "redux-actions": "^2.6.5", "redux-saga": "~1.0.2", "reselect": "^4.0.0", "slick-carousel": "^1.8.1", "swiper": "^11.2.6", "ts-key-enum": "^2.0.2", "universal-cookie": "^3.1.0", "url-parse": "^1.5.1", "zod": "^3.22.2"}}