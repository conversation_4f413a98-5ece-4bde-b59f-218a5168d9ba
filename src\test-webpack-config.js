// Test webpack configuration
const configFactory = require('./scripts/webpack/environments/configurationFactory');
const manifest = require('./Project/SPX/client/manifest.dev.json');

try {
  const configs = configFactory(manifest);
  console.log('✅ Webpack config loaded successfully');
  console.log('Entry points:', Object.keys(configs[0].entry));
  console.log('Tailwind entry found:', configs[0].entry['tailwind-styles']);
  
  // Check if CSS rule exists
  const cssRule = configs[0].module.rules.find(rule => 
    rule.test && rule.test.toString().includes('css') && !rule.test.toString().includes('scss')
  );
  
  if (cssRule) {
    console.log('✅ CSS rule for Tailwind found');
    console.log('CSS loaders:', cssRule.use.map(loader => 
      typeof loader === 'string' ? loader : loader.loader
    ));
  } else {
    console.log('❌ CSS rule for Tailwind not found');
  }
  
} catch (error) {
  console.error('❌ Error loading webpack config:', error.message);
}
