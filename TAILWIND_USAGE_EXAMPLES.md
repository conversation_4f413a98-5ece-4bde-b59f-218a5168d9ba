# Tailwind CSS Usage Examples for NewCheckoutFlow

## Basic Usage in React Components

```tsx
// src/Feature/NewCheckoutFow/components/PaymentButton/PaymentButton.tsx
import React from 'react';

export const PaymentButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
    >
      Complete Payment
    </button>
  );
};
```

## Responsive Design

```tsx
// Responsive checkout form
export const CheckoutForm: React.FC = () => {
  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl">
      <div className="md:flex">
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
            Checkout
          </div>
          <form className="space-y-4 mt-4">
            <input
              type="email"
              placeholder="Email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <input
              type="text"
              placeholder="Card Number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </form>
        </div>
      </div>
    </div>
  );
};
```

## Combining with Existing SCSS

```tsx
// You can mix Tailwind classes with existing CSS classes
export const MixedStyleComponent: React.FC = () => {
  return (
    <div className="checkout-container bg-gray-50 p-6 rounded-lg">
      {/* checkout-container from existing SCSS */}
      {/* bg-gray-50 p-6 rounded-lg from Tailwind */}
      <h2 className="dsm-heading text-2xl font-bold mb-4">
        {/* dsm-heading from existing design system */}
        {/* text-2xl font-bold mb-4 from Tailwind */}
        Order Summary
      </h2>
    </div>
  );
};
```

## Common Utility Patterns

```tsx
// Loading states
<div className="animate-pulse bg-gray-200 h-4 rounded"></div>

// Error states
<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
  Error message
</div>

// Success states
<div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
  Success message
</div>

// Flexbox layouts
<div className="flex items-center justify-between">
  <span>Total:</span>
  <span className="font-bold">$99.99</span>
</div>

// Grid layouts
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Grid items */}
</div>
```

## Performance Tips

1. **Use Tailwind for new components** - Get instant feedback
2. **Keep existing SCSS** - No need to migrate everything at once
3. **Combine strategically** - Use both systems where it makes sense
4. **Purge unused styles** - Tailwind automatically removes unused utilities

## Development Workflow

1. **Start development server**: `npm run start` or `npm run debug`
2. **Add Tailwind classes** to your React components
3. **See instant changes** - No SCSS rebuild required!
4. **Existing SCSS** continues to work as before

## Build Process

The webpack configuration automatically:
- Processes Tailwind CSS separately from SCSS
- Generates optimized CSS bundles
- Maintains existing SCSS functionality
- Provides fast hot-reload for both systems
