// Tailwind CSS v2 configuration
module.exports = {
  purge: [
    // <PERSON>an <PERSON>heckoutFlow components for Tailwind classes
    "./src/Feature/NewCheckoutFow/**/*.{ts,tsx,js,jsx}",
    // Add other paths as needed for testing
    "./src/**/*.{ts,tsx,js,jsx}",
  ],
  darkMode: false, // or 'media' or 'class'
  theme: {
    extend: {
      // Add custom theme extensions here if needed
      // This allows you to extend Tailwind's default theme
      // while maintaining compatibility with your existing design system
    },
  },
  variants: {
    extend: {
      // Add variant extensions here if needed
    },
  },
  plugins: [
    // Add Tailwind plugins here if needed
  ],
  // Ensure compatibility with existing CSS
  corePlugins: {
    // Disable preflight if it conflicts with existing styles
    // preflight: false,
  },
};
