// Simple test to verify Tailwind CSS configuration
const path = require('path');

async function testTailwind() {
  try {
    // Test if tailwindcss can be required
    const tailwindcss = require('tailwindcss');
    console.log('✅ Tailwind CSS module loaded successfully');

    // Test if config file exists and can be loaded
    const configPath = path.resolve('../tailwind.config.js');
    const config = require(configPath);
    console.log('✅ Tailwind config loaded successfully');
    console.log('Content paths:', config.content);

    // Test if we can create a Tailwind processor
    const tailwindProcessor = tailwindcss(config);
    console.log('✅ Tailwind processor created successfully');

    console.log('\n🎉 Tailwind CSS is properly configured and ready to use!');
    console.log('\nNext steps:');
    console.log('1. Run your webpack build to generate CSS');
    console.log('2. Use Tailwind classes in your React components');
    console.log('3. Enjoy fast hot-reload for utility classes!');
  } catch (error) {
    console.error('❌ Error testing Tailwind CSS:', error.message);
    console.error('Stack:', error.stack);
  }
}

testTailwind();
